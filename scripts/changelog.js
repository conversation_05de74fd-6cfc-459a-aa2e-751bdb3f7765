#!/usr/bin/env node

import { execSync } from 'child_process';
import { writeFileSync, existsSync, mkdirSync } from 'fs';
import { join } from 'path';

// Get today's date
const now = new Date();
const year = now.getFullYear();
const month = String(now.getMonth() + 1).padStart(2, '0');
const day = String(now.getDate()).padStart(2, '0');

// Create directory structure
const changelogDir = join(process.cwd(), 'changelogs', year.toString(), month, day);
if (!existsSync(changelogDir)) {
	mkdirSync(changelogDir, { recursive: true });
}

// Find next available number for today
let num = 1;
while (
	existsSync(join(changelogDir, `${year}-${month}-${day}-${String(num).padStart(3, '0')}.md`))
) {
	num++;
}

const filename = `${year}-${month}-${day}-${String(num).padStart(3, '0')}.md`;
const filepath = join(changelogDir, filename);

// Get latest commit info
let commitInfo = '';
try {
	const hash = execSync('git rev-parse --short HEAD', { encoding: 'utf8' }).trim();
	const message = execSync('git log -1 --pretty=%B', { encoding: 'utf8' }).trim();
	// Use --name-only to get full paths relative to repo root
	const files = execSync('git diff --name-only HEAD~1 HEAD', { encoding: 'utf8' }).trim();

	commitInfo = `## Commit: ${hash}
${message}

Files changed:
${files
	.split('\n')
	.filter((f) => f)
	.map((f) => `- ${f}`)
	.join('\n')}
`;
} catch {
	commitInfo = '## No git commit available\n';
}

// Create changelog template
const template = `# ${year}-${month}-${day} #${num}

${commitInfo}

## Summary
[AI to fill: One-line summary of what was done]

## Changes

### [TYPE] Description
**What:** [AI to fill: What changed]
**Why:** [AI to fill: Why it was needed]
**How:** [AI to fill: Technical approach, including key commands used]

\`\`\`typescript
// AI to add relevant code snippets
\`\`\`

## Notes
[AI to fill: Any gotchas, dependencies, or future considerations]
`;

// Write to file
writeFileSync(filepath, template);
console.log(`Created: ${filepath}`);
console.log(`Now read and edit: ${filepath}`);
