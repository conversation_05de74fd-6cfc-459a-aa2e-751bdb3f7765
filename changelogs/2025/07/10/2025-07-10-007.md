# 2025-07-10 #7

## Commit: 6e8ebce
refactor(auth): move user queries to auth module and clean up types

Consolidate user-related query functions into the auth module for better organization and remove the separate queries.ts file. Update type definitions to reflect the changes.

Files changed:
- src/lib/server/auth.ts
- src/lib/server/queries.ts
- src/lib/types/db.ts
- src/routes/(auth)/login/+page.server.ts


## Summary
Consolidated user query functions into the auth module to reduce cognitive load and simplify the codebase architecture.

## Changes

### [REFACTOR] Domain-Driven Organization
**What:** Moved user query functions from a separate `queries.ts` file into the `auth.ts` module and updated all references.
**Why:** To reduce cognitive load by co-locating related functionality, avoid premature abstraction, and simplify the project architecture.
**How:** Relocated the three query functions (`getUserByUsername`, `getUserProfile`, `getActiveUsers`), updated imports in the login page, and adjusted type references in `db.ts`.

```typescript
// BEFORE: Separate queries.ts file with user query functions
// src/lib/server/queries.ts
export async function getUserByUsername(username: string) {
	const [user] = await db.select().from(table.user).where(eq(table.user.username, username));
	return user ?? null;
}

// AFTER: Functions moved to auth.ts with domain context
// src/lib/server/auth.ts
// --- User query functions ---

// Get a user by username (used in login flow)
export async function getUserByUsername(username: string) {
	const [user] = await db.select().from(table.user).where(eq(table.user.username, username));
	return user ?? null;
}

// BEFORE: Import from queries.ts in login page
// src/routes/(auth)/login/+page.server.ts
import { getUserByUsername } from '$lib/server/queries';
// ...
const user = await getUserByUsername(username);

// AFTER: Using auth module directly
// src/routes/(auth)/login/+page.server.ts
import * as auth from '$lib/server/auth';
// ...
const user = await auth.getUserByUsername(username);
```

## Notes

### Benefits
- **Reduced cognitive load**: Related functionality is now co-located in a single file
- **Simplified imports**: Only need to import from `auth` module instead of multiple files
- **Domain-driven organization**: Code is organized by domain purpose rather than technical function
- **Removed unnecessary abstraction**: Eliminated premature separation that added complexity without benefit

### Removed Files
- `src/lib/server/queries.ts` - Functionality moved to auth module

### Future Considerations
- As the auth module grows, consider breaking it into smaller domain-specific modules if needed
- This pattern of co-locating related functionality can be applied to other parts of the codebase
