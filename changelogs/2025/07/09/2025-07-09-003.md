# 2025-07-09 #3

## Commit: 3d4942d

refactor(auth): improve type imports and validation logic

- Replace direct type imports with dynamic imports in Svelte components
- Extract common string validation into reusable function
- Add proper type checking for Postgres errors
- Simplify database query syntax

Files changed:

- src/routes/(auth)/login/+page.server.ts
- src/routes/(auth)/login/+page.svelte
- src/routes/(protected)/dashboard/+page.svelte

## Summary

Refactored authentication code for better type safety, DRY principles, and cleaner imports

## Changes

### Refactor: Generic String Validation

**What:** Created reusable `validateString` function to replace duplicate validation logic
**Why:** Eliminate code duplication between `validateUsername` and `validatePassword` functions
**How:** Extracted common validation pattern into generic function with configurable constraints

```typescript
// Before: Separate validation functions
function validateUsername(username: unknown): string | null {
	if (typeof username !== 'string') return 'Username must be a string';
	if (username.length < 3) return 'Username must be at least 3 characters';
	if (username.length > 31) return 'Username must be at most 31 characters';
	if (!/^[a-zA-Z0-9_]+$/.test(username))
		return 'Username must only contain letters, numbers, and underscores';
	return null;
}

// After: Generic validation with reuse
function validateString(
	value: unknown,
	fieldName: string,
	constraints: {
		min: number;
		max: number;
		pattern?: RegExp;
		patternError?: string;
	}
): string | null {
	if (typeof value !== 'string') return `${fieldName} must be a string`;
	if (value.length < constraints.min)
		return `${fieldName} must be at least ${constraints.min} characters`;
	if (value.length > constraints.max)
		return `${fieldName} must be at most ${constraints.max} characters`;
	if (constraints.pattern && !constraints.pattern.test(value)) {
		return constraints.patternError || `${fieldName} format is invalid`;
	}
	return null;
}
```

### Enhancement: PostgreSQL Error Typing

**What:** Added `PostgresError` interface for better error handling in registration
**Why:** Replace unsafe `unknown` type with proper PostgreSQL error structure
**How:** Created interface and type guard for safe error code checking

```typescript
// Before: Unsafe error handling
catch (error: unknown) {
	if (error && typeof error === 'object' && 'code' in error && error.code === '23505') {
		// Handle duplicate username
	}
}

// After: Type-safe error handling
interface PostgresError {
	code: string;
	message: string;
	detail?: string;
}

function isPostgresError(error: unknown): error is PostgresError {
	return error !== null && typeof error === 'object' && 'code' in error && typeof error.code === 'string';
}

catch (error: unknown) {
	if (isPostgresError(error) && error.code === '23505') {
		// Type-safe duplicate username handling
	}
}
```

### Refactor: Inline Type Imports

**What:** Replaced separate type imports with inline dynamic imports in Svelte components
**Why:** Reduce cognitive load and keep types closer to usage point
**How:** Used `import('./$types').TypeName` syntax instead of dedicated import statements

```typescript
// Before: Separate imports
import type { ActionData } from './$types';
let { form }: { form: ActionData } = $props();

// After: Inline imports
let { form }: { form: import('./$types').ActionData } = $props();
```

### Enhancement: Array Destructuring

**What:** Replaced `.at(0)` with array destructuring for database results
**Why:** Better TypeScript inference and cleaner syntax
**How:** Used `[existingUser]` destructuring pattern

```typescript
// Before: Manual array access
const results = await db.select().from(userTable).where(eq(userTable.username, username));
const existingUser = results.at(0);

// After: Clean destructuring
const [existingUser] = await db.select().from(userTable).where(eq(userTable.username, username));
```

## Notes

- All 9 E2E authentication tests continue to pass after refactoring
- Generic validation function makes adding new field validations trivial
- PostgreSQL error typing improves debugging and error handling reliability
- Inline type imports reduce import clutter while maintaining full type safety
- Code follows Rich Harris principles: minimize code, single source of truth, clarity
