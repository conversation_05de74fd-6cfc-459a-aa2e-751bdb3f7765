# 2025-07-09 #2

## Commit: 93383a4

feat(auth): enhance authentication system and add e2e tests

- Add new test:auth script for playwright auth tests
- Configure playwright to use bun dev server with proper baseURL
- Improve session cookie security with httpOnly, secure, and sameSite
- Expand username validation to allow hyphens and underscores
- Add connection pooling and cleanup for database client
- Simplify auth check by passing event directly to requireLogin
- Add comprehensive e2e tests for all auth flows

Files changed:

- e2e/auth.test.ts
- package.json
- playwright.config.ts
- src/lib/server/auth.ts
- src/lib/server/db/index.ts
- src/routes/(auth)/login/+page.server.ts
- src/routes/(protected)/dashboard/+page.server.ts

## Summary

Fixed E2E test failures and enhanced authentication system with comprehensive test coverage

## Changes

### Fix: E2E Test Button Selectors

**What:** Fixed failing authentication E2E tests by correcting button selectors
**Why:** Tests were timing out because selectors didn't match actual HTML structure
**How:** Updated button selectors to use text-based matching instead of attribute-based

```typescript
// Before: Incorrect selectors causing timeouts
await page.click('button[formaction="?/login"]');
await page.click('button[type="submit"]');

// After: Working text-based selectors
await page.click('button:has-text("Login")');
await page.click('button:has-text("Sign out")');
```

### Enhancement: Type Safety in Authentication

**What:** Improved type safety by passing event object directly to requireLogin
**Why:** Eliminated runtime errors and improved code maintainability
**How:** Removed getRequestEvent dependency and passed event parameter directly

```typescript
// Before: Indirect access with potential runtime issues
const user = await requireLogin();

// After: Direct event passing with type safety
const user = await requireLogin(event);
```

## Notes

- All 10 E2E tests now pass successfully (was 6/10 before)
- Playwright configuration updated to use dev server instead of preview
- Added dedicated `test:auth` script for focused authentication testing
- Button selectors now use `:has-text()` for better reliability
- Type safety improvements reduce potential runtime errors in auth flows
