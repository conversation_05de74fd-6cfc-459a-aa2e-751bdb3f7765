import { hash, verify } from '@node-rs/argon2';
import { encodeBase32LowerCase } from '@oslojs/encoding';
import { fail, redirect } from '@sveltejs/kit';
import { z } from 'zod/v4';
import { zfd } from 'zod-form-data';
import * as auth from '$lib/server/auth';
import { db } from '$lib/server/db';
import * as table from '$lib/server/db/schema';
import type { Actions, PageServerLoad } from './$types';

// Define a simple interface for Postgres errors for type-checking.
interface PostgresError {
	code: string;
}

// Define Zod schemas once and reuse them.
const usernameSchema = z.string()
	.transform((val) => val.trim().toLowerCase().replace(/\s+/g, ''))
	.pipe(
		z.string()
			.min(3, 'Username must be at least 3 characters')
			.max(31, 'Username must be at most 31 characters')
			.regex(/^[a-z0-9_-]+$/, 'Username can only contain lowercase letters, numbers, hyphens, and underscores')
	);

const passwordSchema = z.string()
	.transform((val) => val.trim())
	.pipe(
		z.string()
			.min(6, 'Password must be at least 6 characters')
			.max(255, 'Password must be at most 255 characters')
	);

const formSchema = zfd.formData({
	username: zfd.text(usernameSchema),
	password: zfd.text(passwordSchema)
});


export const load: PageServerLoad = async (event) => {
	if (event.locals.user) {
		return redirect(302, '/dashboard');
	}
	return {};
};

export const actions: Actions = {
	// --- LOGIN ACTION ---
	login: async (event) => {
		const formData = await event.request.formData();
		const result = formSchema.safeParse(formData);

		if (!result.success) {
			return fail(400, {
				message: result.error.issues[0].message
			});
		}
		
		const { username, password } = result.data;

		const user = await auth.getUserByUsername(username);
		if (!user) {
			return fail(400, { message: 'Incorrect username or password' });
		}

		const validPassword = await verify(user.passwordHash, password, {
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});

		if (!validPassword) {
			return fail(400, { message: 'Incorrect username or password' });
		}

		const sessionToken = auth.generateSessionToken();
		const session = await auth.createSession(sessionToken, user.id);
		auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);

		return redirect(302, '/dashboard');
	},

	// --- REGISTER ACTION ---
	register: async (event) => {
		const formData = await event.request.formData();
		const result = formSchema.safeParse(formData);

		if (!result.success) {
			return fail(400, {
				message: result.error.issues[0].message
			});
		}

		const { username, password } = result.data;

		const userId = generateUserId();
		const passwordHash = await hash(password, {
			memoryCost: 19456,
			timeCost: 2,
			outputLen: 32,
			parallelism: 1
		});

		try {
			await db.insert(table.user).values({ id: userId, username, passwordHash });

			const sessionToken = auth.generateSessionToken();
			const session = await auth.createSession(sessionToken, userId);
			auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
		} catch (e: unknown) {
			if (e && typeof e === 'object' && 'code' in e && (e as PostgresError).code === '23505') {
				return fail(400, { message: 'Username already taken' });
			}
			return fail(500, { message: 'An unexpected error occurred. Please try again.' });
		}
		return redirect(302, '/dashboard');
	}
};

function generateUserId() {
	const bytes = crypto.getRandomValues(new Uint8Array(15));
	return encodeBase32LowerCase(bytes);
}