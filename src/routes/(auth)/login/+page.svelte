<script lang="ts">
	import { enhance } from '$app/forms';
	import { FORM_ACTIONS } from '$lib/types/constants';

	let { form }: { form?: { success: boolean; message?: string; redirect?: string } } = $props();

	// Example: TypeScript knows form structure thanks to discriminated unions
	// if (form && !form.success) {
	//   console.log('Error:', form.message); // TypeScript knows this exists
	// }
	// if (form && form.success) {
	//   console.log('Redirect to:', form.redirect); // TypeScript knows this exists
	// }
</script>

<h1>Login/Register</h1>
<form method="post" action={FORM_ACTIONS.LOGIN} use:enhance>
	<label>
		Username
		<input
			name="username"
			class="mt-1 rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
		/>
	</label>
	<label>
		Password
		<input
			type="password"
			name="password"
			class="mt-1 rounded-md border border-gray-300 bg-white px-3 py-2 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none"
		/>
	</label>
	<button class="rounded-md bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700"
		>Login</button
	>
	<button
		formaction={FORM_ACTIONS.REGISTER}
		class="rounded-md bg-blue-600 px-4 py-2 text-white transition hover:bg-blue-700"
		>Register</button
	>
</form>
<p style="color: red">{form?.message ?? ''}</p>
