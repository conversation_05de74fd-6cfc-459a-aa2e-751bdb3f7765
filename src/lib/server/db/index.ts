import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import * as schema from './schema';
import { env } from '$env/dynamic/private';

if (!env.DATABASE_URL) throw new Error('DATABASE_URL is not set');

const client = postgres(env.DATABASE_URL, { max: 10 }); // Pool with max 10 connections
export const db = drizzle(client, { schema });

// Optional: Cleanup on server shutdown
process.on('SIGTERM', async () => {
	await client.end();
});
