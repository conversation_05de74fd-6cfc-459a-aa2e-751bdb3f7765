// Drizzle-generated types + ReturnType patterns for query results
// Leverages Dr<PERSON>zle's built-in type inference instead of manual interfaces


import type * as table from '$lib/server/db/schema';
import type * as auth from '$lib/server/auth';

// Base table types (Drizzle-generated)
export type User = typeof table.user.$inferSelect;
export type UserInsert = typeof table.user.$inferInsert;
export type Session = typeof table.session.$inferSelect;
export type SessionInsert = typeof table.session.$inferInsert;

// Query result types using ReturnType pattern
export type SessionValidationResult = Awaited<ReturnType<typeof auth.validateSessionToken>>;

// Real examples of ReturnType pattern with DB queries
export type UserByUsername = Awaited<ReturnType<typeof auth.getUserByUsername>>;
export type UserProfile = Awaited<ReturnType<typeof auth.getUserProfile>>;
export type ActiveUsers = Awaited<ReturnType<typeof auth.getActiveUsers>>;
export type ActiveUser = ActiveUsers[0]; // Single item from array result

// Generic helpers for custom query results
export type QueryResult<T extends (...args: unknown[]) => unknown> = Awaited<ReturnType<T>>;
export type QueryResultArray<T extends (...args: unknown[]) => unknown> = QueryResult<T> extends readonly unknown[] ? QueryResult<T>[0] : never;

// Usage examples:
// const user: UserByUsername = await auth.getUserByUsername('john');
// const profile: UserProfile = await auth.getUserProfile('user-id');
// const activeUsers: ActiveUsers = await auth.getActiveUsers();
// const firstUser: ActiveUser = activeUsers[0];