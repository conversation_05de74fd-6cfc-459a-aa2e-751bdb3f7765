// Template literal types for type-safe string constants

// <PERSON><PERSON> names
export type CookieName = 'auth-session';
export const COOKIE_NAMES = {
	AUTH_SESSION: 'auth-session' as const
} satisfies Record<string, CookieName>;

// Form action names
export type FormAction = '?/login' | '?/register';
export const FORM_ACTIONS = {
	LOGIN: '?/login' as const,
	REGISTER: '?/register' as const
} satisfies Record<string, FormAction>;

// Action names (without query prefix)
export type ActionName = 'login' | 'register';
export const ACTION_NAMES = {
	LOGIN: 'login' as const,
	REGISTER: 'register' as const
} satisfies Record<string, ActionName>;
