import type { Handle, RequestEvent } from '@sveltejs/kit';
import * as auth from '$lib/server/auth';

// Type-safe event handler with granular App.Locals typing
// Ensures proper typing for auth-related locals and enables better IntelliSense
type AuthLocals = {
	user: App.Locals['user'];
	session: App.Locals['session'];
};

// Helper function for setting auth state with proper typing
function setAuthState(event: RequestEvent, authState: AuthLocals): void {
	event.locals.user = authState.user;
	event.locals.session = authState.session;
}

const handleAuth: Handle = async ({ event, resolve }) => {
	// Type-safe event with explicit App.Locals typing for better IntelliSense
	const sessionToken = event.cookies.get(auth.sessionCookieName);

	if (!sessionToken) {
		setAuthState(event, { user: null, session: null });
		return resolve(event);
	}

	const { session, user } = await auth.validateSessionToken(sessionToken);

	if (session) {
		auth.setSessionTokenCookie(event, sessionToken, session.expiresAt);
	} else {
		auth.deleteSessionTokenCookie(event);
	}

	setAuthState(event, { user, session });
	return resolve(event);
};

export const handle: Handle = handleAuth;
